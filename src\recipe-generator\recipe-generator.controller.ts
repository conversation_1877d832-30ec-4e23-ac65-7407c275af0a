import {
  Body,
  Controller,
  Post,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  RecipeGeneratorService,
  RecipeGeneratorOutput,
} from './recipe-generator.service';

interface ControllerInput {
  url: string;
  language?: string;
}

@Controller('recipe-generator')
export class RecipeGeneratorController {
  constructor(
    private readonly recipeGeneratorService: RecipeGeneratorService,
  ) {}

  /**
   * Detect platform from URL
   */
  private detectPlatform(url: string): 'tiktok' | 'instagram' | 'youtube' {
    const lowerUrl = url.toLowerCase();

    // TikTok patterns
    if (lowerUrl.includes('tiktok.com') || lowerUrl.includes('vm.tiktok.com')) {
      return 'tiktok';
    }

    // Instagram patterns
    if (lowerUrl.includes('instagram.com') || lowerUrl.includes('instagr.am')) {
      return 'instagram';
    }

    // YouTube patterns
    if (lowerUrl.includes('youtube.com') || lowerUrl.includes('youtu.be')) {
      return 'youtube';
    }

    throw new HttpException(
      'Unsupported platform. Only Instagram, TikTok, and YouTube videos are supported.',
      HttpStatus.BAD_REQUEST,
    );
  }

  /**
   * Validate URL format for specific platform
   */
  private validatePlatformUrl(
    url: string,
    platform: 'tiktok' | 'instagram' | 'youtube',
  ): void {
    const patterns = {
      tiktok: /^https?:\/\/(www\.)?(tiktok\.com|vm\.tiktok\.com)\//i,
      instagram: /^https?:\/\/(www\.)?(instagram\.com|instagr\.am)\//i,
      youtube: /^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\//i,
    };

    if (!patterns[platform].test(url)) {
      throw new HttpException(
        `Invalid ${platform} URL format. Please provide a valid ${platform} video URL.`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post('generate')
  async generateRecipe(
    @Body() body: ControllerInput,
  ): Promise<RecipeGeneratorOutput> {
    try {
      // Validate required fields
      if (!body.url) {
        throw new HttpException('URL is required', HttpStatus.BAD_REQUEST);
      }

      // Detect platform from URL
      const platform = this.detectPlatform(body.url);

      // Validate URL format for the detected platform
      this.validatePlatformUrl(body.url, platform);

      // Call the service with the detected platform
      const result = await this.recipeGeneratorService.generateRecipe({
        url: body.url,
        language: body.language || 'en', // Default to English if not specified
        plateform: platform, // Pass the detected platform
      });

      return result;
    } catch (error) {
      // Handle service errors
      if (error instanceof HttpException) {
        throw error;
      }

      // Log the error for debugging
      console.error('Recipe generation error:', error);

      // Return a generic error message to the client
      throw new HttpException(
        'Failed to generate recipe. Please check the URL and try again.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
