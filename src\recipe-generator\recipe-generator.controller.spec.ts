import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { RecipeGeneratorController } from './recipe-generator.controller';
import {
  RecipeGeneratorService,
  RecipeGeneratorInput,
  RecipeGeneratorOutput,
  RecipeOutput,
} from './recipe-generator.service';

describe('RecipeGeneratorController', () => {
  let controller: RecipeGeneratorController;

  const mockRecipe: RecipeOutput = {
    title: 'Test Recipe',
    category: 'Test Category',
    description: 'Test Description',
    cooking_time_minutes: 30,
    servings: 4,
    ingredients: [
      {
        quantity: 1,
        unit: 'cup',
        name: 'flour',
      },
    ],
    steps: [
      {
        name: 'Mix ingredients',
        description: 'Mix all ingredients together',
      },
    ],
    metadata: {
      audio_helpful: true,
      video_type: 'tutorial',
    },
  };

  const mockRecipeGeneratorOutput: RecipeGeneratorOutput = {
    recipe: mockRecipe,
    imagesUrls: [],
  };

  const mockRecipeGeneratorService = {
    generateRecipe: jest.fn<
      Promise<RecipeGeneratorOutput>,
      [RecipeGeneratorInput]
    >(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RecipeGeneratorController],
      providers: [
        {
          provide: RecipeGeneratorService,
          useValue: mockRecipeGeneratorService,
        },
      ],
    }).compile();

    controller = module.get<RecipeGeneratorController>(
      RecipeGeneratorController,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generateRecipe', () => {
    it('should generate a recipe successfully', async () => {
      const input = { url: 'https://www.tiktok.com/@user/video/123456789' };
      mockRecipeGeneratorService.generateRecipe.mockResolvedValue(
        mockRecipeGeneratorOutput,
      );

      const result = await controller.generateRecipe(input);

      expect(mockRecipeGeneratorService.generateRecipe).toHaveBeenCalledWith({
        url: input.url,
        language: 'en',
      });
      expect(result).toEqual(mockRecipeGeneratorOutput);
    });

    it('should use provided language', async () => {
      const input = {
        url: 'https://www.tiktok.com/@user/video/123456789',
        language: 'es',
      };
      mockRecipeGeneratorService.generateRecipe.mockResolvedValue(
        mockRecipeGeneratorOutput,
      );

      await controller.generateRecipe(input);

      expect(mockRecipeGeneratorService.generateRecipe).toHaveBeenCalledWith({
        url: input.url,
        language: 'es',
      });
    });

    it('should throw BadRequest when URL is missing', async () => {
      const input = { url: '' };

      await expect(controller.generateRecipe(input)).rejects.toThrow(
        new HttpException('URL is required', HttpStatus.BAD_REQUEST),
      );
    });

    it('should throw BadRequest for invalid TikTok URL', async () => {
      const input = { url: 'https://www.youtube.com/watch?v=123' };

      await expect(controller.generateRecipe(input)).rejects.toThrow(
        new HttpException('Invalid TikTok URL format', HttpStatus.BAD_REQUEST),
      );
    });

    it('should handle service errors', async () => {
      const input = { url: 'https://www.tiktok.com/@user/video/123456789' };
      mockRecipeGeneratorService.generateRecipe.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(controller.generateRecipe(input)).rejects.toThrow(
        new HttpException(
          'Failed to generate recipe. Please check the URL and try again.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });
  });
});
