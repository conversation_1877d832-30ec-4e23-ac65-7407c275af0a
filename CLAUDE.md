# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Install dependencies
npm install

# Development
npm run start:dev        # Start in watch mode
npm run start:debug      # Start with debug mode

# Build and Production
npm run build           # Build the application
npm run start:prod      # Run production build

# Code Quality
npm run lint            # Run ESLint with auto-fix
npm run format          # Format code with Prettier

# Testing
npm run test            # Run unit tests
npm run test:watch      # Run tests in watch mode
npm run test:cov        # Run tests with coverage
npm run test:e2e        # Run end-to-end tests

# Docker
docker-compose up       # Start with Redis dependency
```

## Architecture

This is a NestJS application that generates recipes from TikTok videos using AI. The core architecture includes:

### Main Components

- **Recipe Generator Module** (`src/recipe-generator/`): Core functionality for downloading TikTok videos, extracting thumbnails, and generating recipes using Google's Gemini AI
- **Recipe Test Module** (`src/recipe-test/`): Quality checking and testing functionality for generated recipes
- **Configuration** (`src/config/`): Centralized configuration management with environment variables

### Key Technologies

- **NestJS**: Main framework with dependency injection and modular architecture
- **BullMQ**: Queue management for background job processing (requires Redis)
- **LangChain**: AI orchestration framework with Google Gemini integration
- **FFmpeg**: Video processing for thumbnail extraction
- **TikTok API**: Video downloading via `@tobyg74/tiktok-api-dl` and `btch-downloader`

### Data Flow

1. TikTok URL input → Video download → Thumbnail extraction
2. Video description + thumbnails → Gemini AI → Structured recipe generation
3. Recipe validation and quality checking
4. Background job processing for heavy operations

### Environment Variables Required

- `GEMINI_API_KEY`: Google Gemini API key for AI functionality
- `REDIS_HOST`: Redis host for BullMQ (default: 'redis')
- `REDIS_PORT`: Redis port (default: 6379)
- `PORT`: Application port (default: 3000)

### File Structure

- `src/recipe-generator/`: Main recipe generation logic and controllers
- `src/recipe-test/`: Recipe testing and quality assurance
- `src/config/`: Configuration factory and type definitions
- `downloads/`: Video files storage
- `thumbnails/`: Extracted thumbnail images

### Important Notes

- Redis is required for queue functionality - use `docker-compose up` for local development
- The application processes video files and generates multiple thumbnails using FFmpeg
- Recipe generation uses structured Zod schemas for type-safe AI responses
- Background jobs handle video processing to avoid blocking the main thread