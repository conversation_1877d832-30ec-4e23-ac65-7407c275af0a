declare module 'youtubei.js' {
  export type YoutubeDownloadType = 'video+audio' | 'video' | 'audio';

  export type YoutubeDownloadOptions = {
    type: YoutubeDownloadType;
    quality?: string;
    format?: string;
  };

  export class Innertube {
    static create(): Promise<Innertube>;

    getInfo(input: string): Promise<unknown>;

    download(
      id: string,
      options: YoutubeDownloadOptions,
    ): Promise<NodeJS.ReadableStream>;
  }
}
