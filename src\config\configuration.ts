export interface AppConfig {
  gemini: {
    apiKey: string;
  };
  redis: {
    host: string;
    port: number;
  };
  app: {
    port: number;
  };
  s3: {
    endpoint: string;
    region: string;
    accessKeyId: string;
    secretAccessKey: string;
    bucket: string;
    publicUrl?: string;
  };
}

export default (): AppConfig => ({
  gemini: {
    apiKey: process.env.GEMINI_API_KEY || '',
  },
  redis: {
    host: process.env.REDIS_HOST || 'redis',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
  },
  app: {
    port: parseInt(process.env.PORT || '3000', 10),
  },
  s3: {
    endpoint: process.env.S3_ENDPOINT || '',
    region: process.env.S3_REGION || 'us-east-1',
    accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
    bucket: process.env.S3_BUCKET || 'cookthat-thumbnails',
    publicUrl: process.env.S3_PUBLIC_URL || '',
  },
});
